# GY-1391: User Import Functionality

## Overview
This PR implements the user import functionality for the auth-mservice, allowing administrators to import users into the system. The implementation includes refactoring the user creation process and adding configuration keys to support the import functionality.

## Changes

### Core Functionality
- Implemented user import functionality in the UserService
- Refactored the user creation process to handle both regular creation and import scenarios
- Added error handling for import failures
- Enhanced post-creation tasks to handle verification emails and notifications

### Code Refactoring
- Refactored the create user under company functionality to improve maintainability
- Improved error handling and logging for user creation processes
- Enhanced validation for user email and company existence

### Configuration Changes
- Added configuration keys for email verification and notification triggers
- Updated deployment configurations for different environments (AWS, GCP)
- Set up proper configuration for email notifications

### Dependency Updates
- Updated base-service version to 4.1.0
- Updated security-lib version to 2.6.2
- Updated Quarkus platform version to 3.13.2

### Deployment Changes
- Reorganized deployment directory structure:
  - Renamed `deploy/qa` to `deploy/aws-qa`
  - Renamed `deploy/staging` to `deploy/aws-staging`
  - Renamed `deploy/uat` to `deploy/gcp-uat`
- Updated deployment configurations for GCP environments
- Added proper configuration for PostgreSQL database connections

## Testing
The changes have been tested in the following scenarios:
- User import with valid data
- User import with invalid data (error handling)
- Email verification after user import
- Notification delivery after user import
- Regular user creation (ensuring backward compatibility)

## Security Considerations
- All imported users follow the same security protocols as manually created users
- Email verification is configurable via the `TRIGGER_VERIFY_EMAIL` environment variable
- Notification emails are configurable via the `TRIGGER_NOTIFY_EMAIL` environment variable

## Deployment Instructions
1. Deploy the updated service to the target environment
2. Ensure the following environment variables are properly set:
   - `TRIGGER_VERIFY_EMAIL`: Set to `true` to send verification emails to imported users
   - `TRIGGER_NOTIFY_EMAIL`: Set to `true` to send notification emails to imported users
   - `CUSTOM_SMTP_USERNAME`: SMTP username for email delivery
   - `CUSTOM_SMTP_PASSWORD`: SMTP password for email delivery

## Related Issues
- [GY-1391](https://jira.glideyoke.com/browse/GY-1391) - Import User functionality
