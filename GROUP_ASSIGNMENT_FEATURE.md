# User Group Assignment Feature

## Overview

This feature enhances the user creation process to automatically assign users to multiple groups. The implementation provides two approaches:

### **Enhanced API (Recommended)**
Uses a dedicated `CreateUserRequest` with a `Set<String> groupNames` field for type-safe, multi-group assignment.

### **Legacy API (Backward Compatible)**
Continues to support the original `AccountRequest` with `groupName` in properties for single group assignment.

The system will:

1. **Find groups by name** - Search for existing groups with the specified names
2. **Create groups if they don't exist** - Automatically create new groups if no existing groups are found
3. **Assign the user to groups** - Add the newly created user to all specified groups

## Implementation Details

### Modified Files

1. **CreateUserRequest.java** *(NEW)*
   - Enhanced request class extending `AccountRequest`
   - Type-safe `Set<String> groupNames` field
   - Built-in validation and convenience methods

2. **UserService.java**
   - Added `UserGroupService` injection
   - Added overloaded `create()` method for `CreateUserRequest`
   - Added `assignUserToGroups()` method for multi-group assignment
   - Maintained backward compatibility with legacy `assignUserToGroup()`

3. **UserGroupService.java**
   - Added `findOrCreateGroupByName()` method to find existing groups or create new ones
   - Added necessary imports for JSON handling and ObjectMapper

4. **CompanyResource.java**
   - Added new endpoint `/companies/{companyId}/user/enhanced` for enhanced user creation
   - Maintained existing endpoint for backward compatibility

### How It Works

#### User Creation Flow
The user creation process now follows this enhanced flow:

1. Validate company
2. Validate user email
3. Create person entity
4. Create Keycloak user
5. Assign roles
6. **[NEW]** Assign user to group (if group name is provided)
7. Handle post-creation tasks

#### Group Assignment Logic
When a `groupName` is provided in the request properties:

1. **Search for existing group**: Query the database for a group with the exact name
2. **Create group if not found**: If no group exists, create a new one with:
   - Name: The specified group name
   - Description: "Auto-created group for user assignment"
3. **Assign user to group**: Add the user to the group using the existing `addUserToGroup()` method

## Usage

### Enhanced API (Recommended)

**Endpoint:** `POST /companies/{companyId}/user/enhanced`

**Request Format:**
```json
{
  "username": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "groupNames": [
    "Development Team",
    "Project Alpha",
    "Backend Engineers"
  ],
  "properties": {
    "email": "<EMAIL>",
    "department": "Engineering"
    // ... other properties
  }
}
```

### Legacy API (Backward Compatible)

**Endpoint:** `POST /companies/{companyId}/user`

**Request Format:**
```json
{
  "username": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "properties": {
    "groupName": "Development Team",
    "email": "<EMAIL>"
    // ... other properties
  }
}
```

### Example Scenarios

#### Scenario 1: Multiple Groups (Enhanced API)
- Request includes `"groupNames": ["Dev Team", "Project Alpha", "Backend"]`
- System finds "Dev Team" (existing), creates "Project Alpha" and "Backend" (new)
- User is assigned to all three groups

#### Scenario 2: Single Group (Legacy API)
- Request includes `"groupName": "Marketing Team"`
- System finds existing "Marketing Team" group
- User is assigned to the existing group

#### Scenario 3: Create New Groups
- Request includes groups that don't exist
- System creates new groups automatically
- User is assigned to the newly created groups

#### Scenario 4: No Group Assignment
- Request doesn't include `groupNames` or `groupName`
- User creation proceeds normally without group assignment
- No additional processing occurs

#### Scenario 5: Partial Failure Handling
- Request includes multiple groups, some assignments fail
- System continues processing other groups instead of failing completely
- Logs errors for failed assignments but completes user creation

## Technical Implementation

### Key Methods

#### UserService.assignUserToGroup()
```java
private Uni<Tuple2<EntityWithPermission, String>> assignUserToGroup(
    String tenantId,
    EntityWithPermission personEntity,
    String authServerId,
    AccountRequest request
)
```
- Extracts group name from request properties
- Calls UserGroupService to find or create group
- Assigns user to group using email

#### UserGroupService.findOrCreateGroupByName()
```java
public EntityWithPermission findOrCreateGroupByName(
    @NonNull String tenantId,
    @NonNull String groupName
)
```
- Searches for existing group by exact name match
- Creates new group if not found
- Returns the group entity

### Error Handling

- **Blank group name**: Throws `BadRequestException` if group name is empty or blank
- **User email not found**: Throws `ServiceException` if user email is missing from person entity
- **Group creation failure**: Rolls back and throws appropriate exceptions
- **Group assignment failure**: Handles rollback of group creation if assignment fails

## Benefits

1. **Streamlined User Management**: Users can be automatically organized into groups during creation
2. **Dynamic Group Creation**: No need to pre-create groups - they're created on-demand
3. **Backward Compatibility**: Existing user creation without group assignment continues to work
4. **Flexible**: Group assignment is optional and controlled by request properties

## Logging

The implementation includes comprehensive logging:
- Info logs for successful group assignments
- Debug logs when no group name is specified
- Error logs for failures with appropriate context

## Future Enhancements

Potential improvements could include:
- Support for multiple group assignments
- Group templates with predefined permissions
- Bulk user-to-group assignment operations
- Group hierarchy support
