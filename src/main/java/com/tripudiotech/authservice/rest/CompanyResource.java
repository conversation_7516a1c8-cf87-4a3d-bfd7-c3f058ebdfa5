/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.request.CreateUserRequest;
import com.tripudiotech.authservice.request.TeamRequest;
import com.tripudiotech.authservice.service.TeamService;
import com.tripudiotech.authservice.service.UserService;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.quarkus.security.Authenticated;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

/**
 * @author: long.nguyen
 */
@Path("/company")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityRequirement(name = "apiToken")
public class CompanyResource extends RestResource {

    @Inject
    UserService userService;

    @Inject
    TeamService teamService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @POST
    @Operation(summary = "Create a User under a Company")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{companyId}/user")
    public Uni<Response> createUser(@PathParam("companyId") String companyId, AccountRequest request) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);

        return userService
                .create(this.tenantId, userInformation, companyId, request)
                .map(
                        entityWithPermission ->
                                Response.status(Response.Status.OK).entity(entityWithPermission).build());
    }

    @POST
    @Operation(summary = "Create a User under a Company with Group Assignment Support")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{companyId}/user/enhanced")
    public Uni<Response> createUserWithGroups(@PathParam("companyId") String companyId, CreateUserRequest request) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);

        return userService
                .create(this.tenantId, userInformation, companyId, request)
                .map(
                        entityWithPermission ->
                                Response.status(Response.Status.OK).entity(entityWithPermission).build());
    }

    @POST
    @Operation(summary = "Create a team under a company")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{companyId}/team")
    public Uni<Response> createTeam(@PathParam("companyId") String companyId, TeamRequest teamRequest) {
        return Uni.createFrom().item(() -> Response.ok(teamService.create(this.tenantId, companyId, teamRequest)).build());
    }
}