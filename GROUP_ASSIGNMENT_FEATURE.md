# User Group Assignment Feature

## Overview

This feature enhances the user creation process to automatically assign users to groups. When creating a user, you can now specify a group name in the request properties, and the system will:

1. **Find the group by name** - Search for an existing group with the specified name
2. **Create the group if it doesn't exist** - Automatically create a new group if no existing group is found
3. **Assign the user to the group** - Add the newly created user to the group

## Implementation Details

### Modified Files

1. **UserService.java**
   - Added `UserGroupService` injection
   - Modified the user creation flow to include group assignment
   - Added `assignUserToGroup()` method to handle group assignment logic

2. **UserGroupService.java**
   - Added `findOrCreateGroupByName()` method to find existing groups or create new ones
   - Added necessary imports for JSON handling and ObjectMapper

### How It Works

#### User Creation Flow
The user creation process now follows this enhanced flow:

1. Validate company
2. Validate user email
3. Create person entity
4. Create Keycloak user
5. Assign roles
6. **[NEW]** Assign user to group (if group name is provided)
7. Handle post-creation tasks

#### Group Assignment Logic
When a `groupName` is provided in the request properties:

1. **Search for existing group**: Query the database for a group with the exact name
2. **Create group if not found**: If no group exists, create a new one with:
   - Name: The specified group name
   - Description: "Auto-created group for user assignment"
3. **Assign user to group**: Add the user to the group using the existing `addUserToGroup()` method

## Usage

### API Request Format

When creating a user, include the `groupName` in the request properties:

```json
{
  "username": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "properties": {
    "groupName": "Development Team",
    "email": "<EMAIL>"
    // ... other properties
  }
}
```

### Example Scenarios

#### Scenario 1: Assign to Existing Group
- Request includes `"groupName": "Marketing Team"`
- System finds existing "Marketing Team" group
- User is assigned to the existing group

#### Scenario 2: Create New Group and Assign
- Request includes `"groupName": "New Project Team"`
- System doesn't find "New Project Team" group
- System creates new group with name "New Project Team"
- User is assigned to the newly created group

#### Scenario 3: No Group Assignment
- Request doesn't include `groupName` property
- User creation proceeds normally without group assignment
- No additional processing occurs

## Technical Implementation

### Key Methods

#### UserService.assignUserToGroup()
```java
private Uni<Tuple2<EntityWithPermission, String>> assignUserToGroup(
    String tenantId,
    EntityWithPermission personEntity,
    String authServerId,
    AccountRequest request
)
```
- Extracts group name from request properties
- Calls UserGroupService to find or create group
- Assigns user to group using email

#### UserGroupService.findOrCreateGroupByName()
```java
public EntityWithPermission findOrCreateGroupByName(
    @NonNull String tenantId, 
    @NonNull String groupName
)
```
- Searches for existing group by exact name match
- Creates new group if not found
- Returns the group entity

### Error Handling

- **Blank group name**: Throws `BadRequestException` if group name is empty or blank
- **User email not found**: Throws `ServiceException` if user email is missing from person entity
- **Group creation failure**: Rolls back and throws appropriate exceptions
- **Group assignment failure**: Handles rollback of group creation if assignment fails

## Benefits

1. **Streamlined User Management**: Users can be automatically organized into groups during creation
2. **Dynamic Group Creation**: No need to pre-create groups - they're created on-demand
3. **Backward Compatibility**: Existing user creation without group assignment continues to work
4. **Flexible**: Group assignment is optional and controlled by request properties

## Logging

The implementation includes comprehensive logging:
- Info logs for successful group assignments
- Debug logs when no group name is specified
- Error logs for failures with appropriate context

## Future Enhancements

Potential improvements could include:
- Support for multiple group assignments
- Group templates with predefined permissions
- Bulk user-to-group assignment operations
- Group hierarchy support
