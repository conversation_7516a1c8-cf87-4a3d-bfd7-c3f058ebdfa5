/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.request;

import com.tripudiotech.authservice.model.GroupReference;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.util.HashSet;
import java.util.Set;

/**
 * Enhanced user creation request that supports group assignment
 * Extends the base AccountRequest with group assignment capabilities
 *
 * <AUTHOR>
 */
@SuperBuilder(toBuilder = true)
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class CreateUserRequest extends AccountRequest {

    /**
     * Set of group references to assign the user to during creation.
     * Supports flexible group identification by ID, name, type, etc.
     * Groups can be created automatically if they don't exist (when using NAME reference).
     *
     * Examples:
     * - By name: GroupReference.byName("Development Team")
     * - By ID: GroupReference.byId("group-123")
     * - With type: GroupReference.byName("Backend Team", GroupType.FUNCTIONAL_TEAM)
     */
    @Valid
    @Size(max = 10, message = "Maximum 10 groups can be assigned to a user")
    @Builder.Default
    Set<GroupReference> groupReferences = new HashSet<>();

    /**
     * Legacy field for backward compatibility with simple group names
     * Will be converted to GroupReference objects internally
     *
     * @deprecated Use groupReferences instead for more flexibility
     */
    @Deprecated
    Set<String> groupNames;

    /**
     * Convenience method to add a group reference
     *
     * @param groupReference the group reference to add
     * @return this request for method chaining
     */
    public CreateUserRequest addGroupReference(GroupReference groupReference) {
        if (this.groupReferences == null) {
            this.groupReferences = new HashSet<>();
        }
        this.groupReferences.add(groupReference);
        return this;
    }

    /**
     * Convenience method to add a group by name
     *
     * @param groupName the name of the group to add
     * @return this request for method chaining
     */
    public CreateUserRequest addGroupName(String groupName) {
        return addGroupReference(new GroupReference(groupName));
    }

    /**
     * Convenience method to add a group by ID
     *
     * @param groupId the ID of the group to add
     * @return this request for method chaining
     */
    public CreateUserRequest addGroupById(String groupId) {
        return addGroupReference(GroupReference.byId(groupId));
    }

    /**
     * Convenience method to check if any groups are specified
     *
     * @return true if groups are specified, false otherwise
     */
    public boolean hasGroups() {
        return (groupReferences != null && !groupReferences.isEmpty()) ||
               (groupNames != null && !groupNames.isEmpty());
    }

    /**
     * Get all group references, including converted legacy group names
     *
     * @return set of all group references
     */
    public Set<GroupReference> getAllGroupReferences() {
        Set<GroupReference> allReferences = new HashSet<>();

        // Add explicit group references
        if (groupReferences != null) {
            allReferences.addAll(groupReferences);
        }

        // Convert legacy group names to references
        if (groupNames != null) {
            for (String groupName : groupNames) {
                allReferences.add(new GroupReference(groupName));
            }
        }

        return allReferences;
    }

    /**
     * Get group references as a defensive copy
     *
     * @return copy of group references set
     */
    public Set<GroupReference> getGroupReferences() {
        return groupReferences == null ? new HashSet<>() : new HashSet<>(groupReferences);
    }

    /**
     * Legacy method for backward compatibility
     *
     * @deprecated Use getAllGroupReferences() instead
     */
    @Deprecated
    public Set<String> getGroupNames() {
        return groupNames == null ? new HashSet<>() : new HashSet<>(groupNames);
    }
}
