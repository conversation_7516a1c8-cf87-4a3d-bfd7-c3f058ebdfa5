/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.response;

import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Set;

/**
 * @author: long.nguyen
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AccountCreatedResponse extends EntityWithPermission {

    @NonNull
    String authServerId;

    String companyId;

    @NonNull
    Set<String> roles;

    public AccountCreatedResponse(@NonNull String authServerId,
                                  @NonNull String companyId,
                                  @NonNull EntityWithPermission entityWithPermission) {
        this.setId(entityWithPermission.getId());
        this.setAuthServerId(authServerId);
        this.setCompanyId(companyId);
        this.setCreatedAt(entityWithPermission.getCreatedAt());
        this.setUpdatedAt(entityWithPermission.getUpdatedAt());
        this.setPermissions(entityWithPermission.getPermissions());
        this.setProperties(entityWithPermission.getProperties());
        this.setState(entityWithPermission.getState());
        this.setOwner(entityWithPermission.getOwner());
        this.setCreatedBy(entityWithPermission.getCreatedBy());
        this.setDisabled(entityWithPermission.isDisabled());
    }

    public AccountCreatedResponse(@NonNull UserRealmInformationResponse userRealmInformationResponse,
                                  @NonNull String companyId,
                                  @NonNull EntityWithPermission entityWithPermission) {
        this.setId(entityWithPermission.getId());
        this.setAuthServerId(userRealmInformationResponse.getId());
        this.setCompanyId(companyId);
        this.setCreatedAt(entityWithPermission.getCreatedAt());
        this.setUpdatedAt(entityWithPermission.getUpdatedAt());
        this.setPermissions(entityWithPermission.getPermissions());
        this.setProperties(entityWithPermission.getProperties());
        // firstname & lastname are 2 fields in keycloak and we don't have it in neo4j, therefore we need to add it manually
        this.getProperties().put(UserInformation.Fields.firstName, userRealmInformationResponse.getFirstName());
        this.getProperties().put(UserInformation.Fields.lastName, userRealmInformationResponse.getLastName());
        this.setState(entityWithPermission.getState());
        this.setOwner(entityWithPermission.getOwner());
        this.setCreatedBy(entityWithPermission.getCreatedBy());
        this.setDisabled(entityWithPermission.isDisabled());
        this.setRoles(userRealmInformationResponse.getRoles());
    }


}
