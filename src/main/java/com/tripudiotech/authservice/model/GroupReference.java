/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;

/**
 * Value object representing a reference to a group.
 * Supports flexible group identification by ID, name, or other criteria.
 * 
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GroupReference {

    /**
     * The value used to identify the group (ID, name, etc.)
     */
    @NotBlank(message = "Group reference value cannot be blank")
    String value;

    /**
     * The type of reference (ID, NAME, etc.)
     */
    @NotNull(message = "Group reference type cannot be null")
    @Builder.Default
    GroupReferenceType type = GroupReferenceType.NAME;

    /**
     * Optional group type/category for additional context
     */
    GroupType groupType;

    /**
     * Whether to create the group if it doesn't exist
     * Only applicable when type is NAME
     */
    @Builder.Default
    boolean createIfNotExists = true;

    /**
     * Optional description for auto-created groups
     */
    String description;

    /**
     * JSON constructor for deserialization
     */
    @JsonCreator
    public GroupReference(
            @JsonProperty("value") String value,
            @JsonProperty("type") GroupReferenceType type,
            @JsonProperty("groupType") GroupType groupType,
            @JsonProperty("createIfNotExists") Boolean createIfNotExists,
            @JsonProperty("description") String description) {
        this.value = value;
        this.type = type != null ? type : GroupReferenceType.NAME;
        this.groupType = groupType;
        this.createIfNotExists = createIfNotExists != null ? createIfNotExists : true;
        this.description = description;
    }

    /**
     * Convenience constructor for name-based reference
     */
    public GroupReference(String name) {
        this(name, GroupReferenceType.NAME, null, true, null);
    }

    /**
     * Convenience constructor for ID-based reference
     */
    public static GroupReference byId(String id) {
        return GroupReference.builder()
                .value(id)
                .type(GroupReferenceType.ID)
                .createIfNotExists(false)
                .build();
    }

    /**
     * Convenience constructor for name-based reference with type
     */
    public static GroupReference byName(String name, GroupType groupType) {
        return GroupReference.builder()
                .value(name)
                .type(GroupReferenceType.NAME)
                .groupType(groupType)
                .createIfNotExists(true)
                .build();
    }

    /**
     * Convenience constructor for name-based reference with description
     */
    public static GroupReference byName(String name, String description) {
        return GroupReference.builder()
                .value(name)
                .type(GroupReferenceType.NAME)
                .createIfNotExists(true)
                .description(description)
                .build();
    }

    /**
     * Check if this reference is by ID
     */
    public boolean isById() {
        return GroupReferenceType.ID.equals(type);
    }

    /**
     * Check if this reference is by name
     */
    public boolean isByName() {
        return GroupReferenceType.NAME.equals(type);
    }

    /**
     * Check if group should be created if not found
     */
    public boolean shouldCreateIfNotExists() {
        return createIfNotExists && isByName();
    }

    /**
     * Get effective description for group creation
     */
    public String getEffectiveDescription() {
        if (description != null) {
            return description;
        }
        return "Auto-created group" + (groupType != null ? " (" + groupType.getDisplayName() + ")" : "");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupReference that = (GroupReference) o;
        return Objects.equals(value, that.value) && type == that.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(value, type);
    }

    @Override
    public String toString() {
        return String.format("GroupReference{%s='%s'%s}", 
                type.name(), value,
                groupType != null ? ", type=" + groupType : "");
    }
}
