/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enum representing different types/categories of groups
 * 
 * <AUTHOR>
 */
public enum GroupType {
    
    /**
     * Organizational department (HR, Engineering, Marketing, etc.)
     */
    DEPARTMENT("department", "Department"),
    
    /**
     * Project-based team
     */
    PROJECT_TEAM("projectTeam", "Project Team"),
    
    /**
     * Role-based group (Admins, Developers, etc.)
     */
    ROLE_GROUP("roleGroup", "Role Group"),
    
    /**
     * Functional team (Backend, Frontend, DevOps, etc.)
     */
    FUNCTIONAL_TEAM("functionalTeam", "Functional Team"),
    
    /**
     * Geographic/location-based group
     */
    LOCATION("location", "Location"),
    
    /**
     * Temporary group for specific purposes
     */
    TEMPORARY("temporary", "Temporary Group"),
    
    /**
     * Security group for access control
     */
    SECURITY("security", "Security Group"),
    
    /**
     * Custom group type
     */
    CUSTOM("custom", "Custom Group");

    private final String value;
    private final String displayName;

    GroupType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static GroupType fromValue(String value) {
        for (GroupType type : GroupType.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown GroupType: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
