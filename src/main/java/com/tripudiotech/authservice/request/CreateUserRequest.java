/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.request;

import com.tripudiotech.base.client.dto.request.AccountRequest;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.HashSet;
import java.util.Set;

/**
 * Enhanced user creation request that supports group assignment
 * Extends the base AccountRequest with group assignment capabilities
 * 
 * <AUTHOR>
 */
@SuperBuilder(toBuilder = true)
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class CreateUserRequest extends AccountRequest {

    /**
     * Set of group names to assign the user to during creation.
     * Groups will be created automatically if they don't exist.
     * 
     * Example: ["Development Team", "Project Alpha", "Backend Engineers"]
     */
    @Size(max = 10, message = "Maximum 10 groups can be assigned to a user")
    Set<@NotEmpty(message = "Group name cannot be empty") String> groupNames = new HashSet<>();

    /**
     * Convenience method to add a single group name
     * 
     * @param groupName the name of the group to add
     * @return this request for method chaining
     */
    public CreateUserRequest addGroupName(String groupName) {
        if (this.groupNames == null) {
            this.groupNames = new HashSet<>();
        }
        this.groupNames.add(groupName);
        return this;
    }

    /**
     * Convenience method to check if any groups are specified
     * 
     * @return true if groups are specified, false otherwise
     */
    public boolean hasGroups() {
        return groupNames != null && !groupNames.isEmpty();
    }

    /**
     * Get group names as a defensive copy
     * 
     * @return copy of group names set
     */
    public Set<String> getGroupNames() {
        return groupNames == null ? new HashSet<>() : new HashSet<>(groupNames);
    }
}
