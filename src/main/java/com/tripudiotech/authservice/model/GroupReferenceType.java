/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enum representing different ways to reference a group
 * 
 * <AUTHOR>
 */
public enum GroupReferenceType {
    
    /**
     * Reference group by its unique identifier
     */
    ID("id", "Group ID"),
    
    /**
     * Reference group by its name (supports auto-creation)
     */
    NAME("name", "Group Name"),
    
    /**
     * Reference group by external identifier
     */
    EXTERNAL_ID("externalId", "External ID"),
    
    /**
     * Reference group by code/slug
     */
    CODE("code", "Group Code");

    private final String value;
    private final String displayName;

    GroupReferenceType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static GroupReferenceType fromValue(String value) {
        for (GroupReferenceType type : GroupReferenceType.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown GroupReferenceType: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
